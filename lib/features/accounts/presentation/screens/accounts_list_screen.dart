import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/data/models/account.dart';
import 'package:budapp/features/accounts/presentation/widgets/account_card.dart';
import 'package:budapp/features/accounts/presentation/widgets/empty_accounts_state.dart';
import 'package:budapp/features/accounts/providers/account_providers.dart';
import 'package:budapp/features/common/widgets/app_bar_helpers.dart';
import 'package:budapp/l10n/app_localizations.dart';
import 'package:budapp/widgets/navigation/global_fab_system.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

/// Screen displaying a list of all user accounts with filtering and management options
class AccountsListScreen extends ConsumerStatefulWidget {
  const AccountsListScreen({super.key});

  @override
  ConsumerState<AccountsListScreen> createState() => _AccountsListScreenState();
}

class _AccountsListScreenState extends ConsumerState<AccountsListScreen> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final l10n = AppLocalizations.of(context)!;
    final accountsAsync = ref.watch(accountListProvider);

    return Scaffold(
      appBar: AppBarHelpers.createStandardScrollableAppBar(
        title: l10n.accounts,
        automaticallyImplyLeading: false, // Use global FAB back button instead
        actions: [
          TextButton(
            onPressed: () => context.push('/accounts/create'),
            child: const Text('+ Add Account'),
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          ref.invalidate(accountListProvider);
        },
        child: accountsAsync.when(
          data: (accounts) => _buildAccountsList(accounts, theme, l10n),
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => _buildErrorState(error, theme, l10n),
        ),
      ),
    ).withGlobalFabs();
  }

  Widget _buildAccountsList(
    List<Account> accounts,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    if (accounts.isEmpty) {
      return const EmptyAccountsState();
    }

    // Group accounts by classification
    final assetAccounts = accounts
        .where(
          (account) => account.classification == AccountClassification.asset,
        )
        .toList();
    final liabilityAccounts = accounts
        .where(
          (account) =>
              account.classification == AccountClassification.liability,
        )
        .toList();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppSpacing.md),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Net Worth Summary Card
          _buildNetWorthSummaryCard(
            assetAccounts,
            liabilityAccounts,
            theme,
            l10n,
          ),
          const SizedBox(height: AppSpacing.lg),

          // Asset accounts section
          if (assetAccounts.isNotEmpty) ...[
            _buildSectionHeader(l10n.assets, assetAccounts.length, theme),
            const SizedBox(height: AppSpacing.sm),
            // PERFORMANCE OPTIMIZATION: Use ListView.builder for better memory management
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: assetAccounts.length,
              itemBuilder: (context, index) {
                final account = assetAccounts[index];
                return Padding(
                  padding: const EdgeInsets.only(bottom: AppSpacing.sm),
                  child: AccountCard(
                    key: ValueKey(
                      account.id,
                    ), // Add key for better widget recycling
                    account: account,
                    onTap: () =>
                        context.push('/transactions/account/${account.id}'),
                    onEdit: () => context.push('/accounts/${account.id}/edit'),
                    showActions: true,
                  ),
                );
              },
            ),
            const SizedBox(height: AppSpacing.lg),
          ],

          // Liability accounts section
          if (liabilityAccounts.isNotEmpty) ...[
            _buildSectionHeader(
              l10n.liabilities,
              liabilityAccounts.length,
              theme,
            ),
            const SizedBox(height: AppSpacing.sm),
            // PERFORMANCE OPTIMIZATION: Use ListView.builder for better memory management
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: liabilityAccounts.length,
              itemBuilder: (context, index) {
                final account = liabilityAccounts[index];
                return Padding(
                  padding: const EdgeInsets.only(bottom: AppSpacing.sm),
                  child: AccountCard(
                    key: ValueKey(
                      account.id,
                    ), // Add key for better widget recycling
                    account: account,
                    onTap: () =>
                        context.push('/transactions/account/${account.id}'),
                    onEdit: () => context.push('/accounts/${account.id}/edit'),
                    showActions: true,
                  ),
                );
              },
            ),
          ],

          // Bottom padding for FAB
          const SizedBox(height: 80),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title, int count, ThemeData theme) {
    return Row(
      children: [
        Text(
          title,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: AppTypography.fontWeightSemiBold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(width: AppSpacing.sm),
        Container(
          padding: const EdgeInsets.symmetric(
            horizontal: AppSpacing.sm,
            vertical: AppSpacing.xs,
          ),
          decoration: BoxDecoration(
            color: theme.colorScheme.primaryContainer,
            borderRadius: BorderRadius.circular(AppBorderRadius.lg),
          ),
          child: Text(
            count.toString(),
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onPrimaryContainer,
              fontWeight: AppTypography.fontWeightMedium,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildErrorState(
    Object error,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.lg),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: theme.colorScheme.error),
            const SizedBox(height: AppSpacing.md),
            Text(
              l10n.errorLoadingAccounts,
              style: theme.textTheme.headlineSmall?.copyWith(
                color: theme.colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppSpacing.sm),
            Text(
              error.toString(),
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppSpacing.lg),
            FilledButton(
              onPressed: () => ref.invalidate(accountListProvider),
              child: Text(l10n.retry),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNetWorthSummaryCard(
    List<Account> assetAccounts,
    List<Account> liabilityAccounts,
    ThemeData theme,
    AppLocalizations l10n,
  ) {
    // Calculate totals
    final totalAssets = assetAccounts.fold<int>(
      0,
      (sum, account) => sum + account.currentBalanceCents,
    );
    final totalLiabilities = liabilityAccounts.fold<int>(
      0,
      (sum, account) => sum + account.currentBalanceCents.abs(),
    );
    final netWorth = totalAssets - totalLiabilities;

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppSpacing.lg),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.account_balance_wallet,
                  color: theme.colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: AppSpacing.sm),
                Text(
                  'Net Worth',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppSpacing.lg),

            // Net Worth Amount
            Text(
              _formatCurrency(netWorth),
              style: theme.textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: netWorth >= 0
                    ? theme.colorScheme.primary
                    : theme.colorScheme.error,
              ),
            ),
            const SizedBox(height: AppSpacing.lg),

            // Assets and Debts breakdown
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'Assets',
                    totalAssets,
                    theme.colorScheme.primary,
                    theme,
                  ),
                ),
                const SizedBox(width: AppSpacing.md),
                Expanded(
                  child: _buildSummaryItem(
                    'Debts',
                    totalLiabilities,
                    theme.colorScheme.error,
                    theme,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(
    String label,
    int amountCents,
    Color color,
    ThemeData theme,
  ) {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppBorderRadius.md),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: AppSpacing.xs),
          Text(
            _formatCurrency(amountCents),
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onSurface,
            ),
          ),
        ],
      ),
    );
  }

  String _formatCurrency(int amountCents) {
    final amount = amountCents / 100;
    return '\$${amount.toStringAsFixed(2)}';
  }
}
