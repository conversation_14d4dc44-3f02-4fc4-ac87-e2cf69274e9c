import 'package:budapp/config/design_tokens.dart';
import 'package:budapp/features/common/providers/time_period_providers.dart';
import 'package:budapp/features/common/widgets/app_bar_helpers.dart';
import 'package:budapp/features/common/widgets/horizontal_period_selector.dart';
import 'package:budapp/features/common/widgets/time_period_modal.dart';
import 'package:budapp/features/common/widgets/time_period_selector.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../helpers/test_wrapper.dart';

// Mock classes for testing
class MockTimePeriodNotifier extends Mock implements TimePeriodNotifier {}

void main() {
  group('AppBarHelpers', () {
    group('createTimePeriodAppBar', () {
      testWidgets('creates AppBar with TimePeriodSelector and title', (
        tester,
      ) async {
        final appBar = AppBarHelpers.createTimePeriodAppBar(
          title: 'Test Screen',
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Scaffold(appBar: appBar, body: const SizedBox()),
          ),
        );

        // Should find the AppBar
        expect(find.byType(AppBar), findsOneWidget);

        // Should find calendar button (replaces TimePeriodSelector)
        expect(find.byIcon(Icons.calendar_month), findsOneWidget);

        // Should find the title text
        expect(find.text('Test Screen'), findsOneWidget);

        // Should find the Row layout in title
        expect(find.byType(Row), findsAtLeastNWidgets(1));
      });

      testWidgets('includes actions when provided', (tester) async {
        final appBar = AppBarHelpers.createTimePeriodAppBar(
          title: 'Test Screen',
          actions: [
            IconButton(onPressed: () {}, icon: const Icon(Icons.search)),
          ],
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Scaffold(appBar: appBar, body: const SizedBox()),
          ),
        );

        // Should find the action button
        expect(find.byIcon(Icons.search), findsOneWidget);
      });

      testWidgets('includes bottom widget when provided', (tester) async {
        final appBar = AppBarHelpers.createTimePeriodAppBar(
          title: 'Test Screen',
          bottom: const TabBar(
            tabs: [
              Tab(text: 'Tab 1'),
              Tab(text: 'Tab 2'),
            ],
          ),
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            DefaultTabController(
              length: 2,
              child: Scaffold(appBar: appBar, body: const SizedBox()),
            ),
          ),
        );

        // Should find the TabBar
        expect(find.byType(TabBar), findsOneWidget);
        expect(find.text('Tab 1'), findsOneWidget);
        expect(find.text('Tab 2'), findsOneWidget);
      });
    });

    group('createTimePeriodScrollableAppBar', () {
      testWidgets('creates AppBar with TimePeriodSelector and title', (
        tester,
      ) async {
        final appBar = AppBarHelpers.createTimePeriodScrollableAppBar(
          title: 'Test Screen',
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Scaffold(
              appBar: appBar,
              body: Container(height: 1000, color: Colors.blue),
            ),
          ),
        );

        // Should find the AppBar
        expect(find.byType(AppBar), findsOneWidget);

        // Should find calendar button (replaces TimePeriodSelector)
        expect(find.byIcon(Icons.calendar_month), findsOneWidget);

        // Should find the title text
        expect(find.text('Test Screen'), findsOneWidget);
      });

      testWidgets('creates AppBar with proper configuration', (tester) async {
        final appBar = AppBarHelpers.createTimePeriodScrollableAppBar(
          title: 'Test Screen',
        );

        // Should be an AppBar instance
        expect(appBar, isA<AppBar>());

        // Should have the correct title
        expect(appBar.title, isA<Widget>());

        // Should have proper elevation
        expect(appBar.elevation, equals(AppElevation.sm));
      });
    });

    group('createStandardAppBar', () {
      testWidgets('creates AppBar with title only', (tester) async {
        final appBar = AppBarHelpers.createStandardAppBar(title: 'Profile');

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Scaffold(appBar: appBar, body: const SizedBox()),
          ),
        );

        // Should find the AppBar
        expect(find.byType(AppBar), findsOneWidget);

        // Should find the title text
        expect(find.text('Profile'), findsOneWidget);

        // Should NOT find TimePeriodSelector
        expect(find.byType(TimePeriodSelector), findsNothing);
      });

      testWidgets('centers title by default', (tester) async {
        final appBar = AppBarHelpers.createStandardAppBar(title: 'Profile');

        // Check that centerTitle is false by default (left-aligned titles)
        expect(appBar.centerTitle, isFalse);
      });

      testWidgets('includes actions when provided', (tester) async {
        final appBar = AppBarHelpers.createStandardAppBar(
          title: 'Profile',
          actions: [
            IconButton(onPressed: () {}, icon: const Icon(Icons.settings)),
          ],
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Scaffold(appBar: appBar, body: const SizedBox()),
          ),
        );

        // Should find the action button
        expect(find.byIcon(Icons.settings), findsOneWidget);
      });
    });

    group('createStandardScrollableAppBar', () {
      testWidgets('creates AppBar with title only', (tester) async {
        final appBar = AppBarHelpers.createStandardScrollableAppBar(
          title: 'Categories',
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Scaffold(
              appBar: appBar,
              body: Container(height: 1000, color: Colors.blue),
            ),
          ),
        );

        // Should find the AppBar
        expect(find.byType(AppBar), findsOneWidget);

        // Should find the title text
        expect(find.text('Categories'), findsOneWidget);

        // Should NOT find TimePeriodSelector
        expect(find.byType(TimePeriodSelector), findsNothing);
      });

      testWidgets('creates AppBar with proper configuration', (tester) async {
        final appBar = AppBarHelpers.createStandardScrollableAppBar(
          title: 'Categories',
        );

        // Should be an AppBar instance
        expect(appBar, isA<AppBar>());

        // Should have the correct title
        expect(appBar.title, isA<Widget>());

        // Should have proper elevation
        expect(appBar.elevation, equals(AppElevation.sm));
      });
    });

    group('createHorizontalTimePeriodAppBar', () {
      testWidgets('creates Column with AppBar and HorizontalPeriodSelector', (
        tester,
      ) async {
        final appBarColumn = AppBarHelpers.createHorizontalTimePeriodAppBar(
          title: 'Test Screen',
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Scaffold(
              body: Column(
                children: [
                  appBarColumn,
                  const Expanded(child: SizedBox()),
                ],
              ),
            ),
          ),
        );
        
        await tester.pumpAndSettle();

        // Should find the Column containing AppBar and HorizontalPeriodSelector
        expect(find.byType(Column), findsAtLeastNWidgets(1));
        
        // Should find the AppBar
        expect(find.byType(AppBar), findsOneWidget);
        
        // Should find the HorizontalPeriodSelector
        expect(find.byType(HorizontalPeriodSelector), findsOneWidget);

        // Should find calendar button (fallback modal selector)
        expect(find.byIcon(Icons.calendar_month), findsOneWidget);

        // Should find the title text
        expect(find.text('Test Screen'), findsOneWidget);
      });

      testWidgets('includes actions when provided', (tester) async {
        final appBarColumn = AppBarHelpers.createHorizontalTimePeriodAppBar(
          title: 'Test Screen',
          actions: [
            IconButton(onPressed: () {}, icon: const Icon(Icons.search)),
          ],
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Scaffold(
              body: Column(
                children: [
                  appBarColumn,
                  const Expanded(child: SizedBox()),
                ],
              ),
            ),
          ),
        );
        
        await tester.pumpAndSettle();

        // Should find the action button
        expect(find.byIcon(Icons.search), findsOneWidget);
        // Should also find the calendar button
        expect(find.byIcon(Icons.calendar_month), findsOneWidget);
      });

      testWidgets('passes allowFutureNavigation to HorizontalPeriodSelector', (
        tester,
      ) async {
        final appBarColumn = AppBarHelpers.createHorizontalTimePeriodAppBar(
          title: 'Budget Screen',
          allowFutureNavigation: true,
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Scaffold(
              body: Column(
                children: [
                  appBarColumn,
                  const Expanded(child: SizedBox()),
                ],
              ),
            ),
          ),
        );
        
        await tester.pumpAndSettle();

        // Find the HorizontalPeriodSelector and verify it exists
        final horizontalSelectorFinder = find.byType(HorizontalPeriodSelector);
        expect(horizontalSelectorFinder, findsOneWidget);
        
        // Get the widget to check allowFutureNavigation property
        final horizontalSelector = tester.widget<HorizontalPeriodSelector>(
          horizontalSelectorFinder,
        );
        expect(horizontalSelector.allowFutureNavigation, isTrue);
      });

      testWidgets('includes bottom widget when provided', (tester) async {
        final appBarColumn = AppBarHelpers.createHorizontalTimePeriodAppBar(
          title: 'Test Screen',
          bottom: const TabBar(
            tabs: [
              Tab(text: 'Tab 1'),
              Tab(text: 'Tab 2'),
            ],
          ),
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            DefaultTabController(
              length: 2,
              child: Scaffold(
                body: Column(
                  children: [
                    appBarColumn,
                    const Expanded(child: SizedBox()),
                  ],
                ),
              ),
            ),
          ),
        );
        
        await tester.pumpAndSettle();

        // Should find the TabBar
        expect(find.byType(TabBar), findsOneWidget);
        expect(find.text('Tab 1'), findsOneWidget);
        expect(find.text('Tab 2'), findsOneWidget);
      });

      testWidgets('has proper styling and layout', (tester) async {
        final appBarColumn = AppBarHelpers.createHorizontalTimePeriodAppBar(
          title: 'Test Screen',
          backgroundColor: Colors.blue,
          elevation: 4,
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Scaffold(
              body: Column(
                children: [
                  appBarColumn,
                  const Expanded(child: SizedBox()),
                ],
              ),
            ),
          ),
        );
        
        await tester.pumpAndSettle();

        // Find the main column
        final columnFinder = find.byType(Column).first;
        expect(columnFinder, findsOneWidget);
        
        final column = tester.widget<Column>(columnFinder);
        expect(column.mainAxisSize, equals(MainAxisSize.min));
        expect(column.children.length, equals(2)); // AppBar + Container with HorizontalPeriodSelector
        
        // Verify the horizontal selector container has proper styling
        final containerFinder = find.descendant(
          of: columnFinder,
          matching: find.byType(Container),
        );
        
        // Should find at least one container (the one wrapping HorizontalPeriodSelector)
        expect(containerFinder, findsAtLeastNWidgets(1));
      });

      testWidgets('automatically implies leading when specified', (tester) async {
        final appBarColumn = AppBarHelpers.createHorizontalTimePeriodAppBar(
          title: 'Test Screen',
          automaticallyImplyLeading: true,
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Navigator(
              onGenerateRoute: (settings) {
                return MaterialPageRoute(
                  builder: (context) => Scaffold(
                    body: Column(
                      children: [
                        appBarColumn,
                        const Expanded(child: SizedBox()),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        );
        
        await tester.pumpAndSettle();

        // Should find the AppBar
        expect(find.byType(AppBar), findsOneWidget);
        
        // Get AppBar widget to check automaticallyImplyLeading property
        final appBar = tester.widget<AppBar>(find.byType(AppBar));
        expect(appBar.automaticallyImplyLeading, isTrue);
      });
    });

    group('_CalendarPeriodButton', () {
      testWidgets('shows modal when tapped', (tester) async {
        final appBar = AppBarHelpers.createTimePeriodAppBar(
          title: 'Test Screen',
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Scaffold(appBar: appBar, body: const SizedBox()),
          ),
        );
        
        await tester.pumpAndSettle();

        // Find and tap the calendar button
        final calendarButton = find.byIcon(Icons.calendar_month);
        expect(calendarButton, findsOneWidget);
        
        await tester.tap(calendarButton);
        await tester.pumpAndSettle();

        // Should show the modal
        expect(find.byType(TimePeriodModal), findsOneWidget);
      });

      testWidgets('has proper tooltip', (tester) async {
        final appBar = AppBarHelpers.createTimePeriodAppBar(
          title: 'Test Screen',
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Scaffold(appBar: appBar, body: const SizedBox()),
          ),
        );
        
        await tester.pumpAndSettle();

        // Find the calendar button
        final calendarButton = find.byIcon(Icons.calendar_month);
        expect(calendarButton, findsOneWidget);
        
        // Long press to show tooltip
        await tester.longPress(calendarButton);
        await tester.pumpAndSettle();

        // Should show tooltip
        expect(find.text('Select Period'), findsOneWidget);
      });

      testWidgets('respects allowFutureNavigation parameter', (tester) async {
        final appBar = AppBarHelpers.createTimePeriodScrollableAppBar(
          title: 'Test Screen',
          allowFutureNavigation: true,
        );

        await tester.pumpWidget(
          TestWrapper.createTestWidget(
            Scaffold(appBar: appBar, body: const SizedBox()),
          ),
        );
        
        await tester.pumpAndSettle();

        // The calendar button should exist and be functional
        final calendarButton = find.byIcon(Icons.calendar_month);
        expect(calendarButton, findsOneWidget);
        
        // Tap should work without errors
        await tester.tap(calendarButton);
        await tester.pumpAndSettle();

        // Modal should appear
        expect(find.byType(TimePeriodModal), findsOneWidget);
      });
    });
  });
}
